import React, { useState } from "react";
import { CarouselHeroBanner } from "../components/common/CarouselHeroBanner";
import { HotCategories } from "../components/common/Categories";
import {
  PopularMangaCarousel,
  RecentMangaCarousel,
  RecommendedMangaCarousel,
} from "../components/common/MangaCarousel";
import type { Category } from "../components/common/Categories";
import type { MangaData } from "../components/common/MangaCard";

export const HomePage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] =
    useState<string>("all-category");

  // Hero slides data
  const heroSlides = [
    {
      id: "one-piece",
      title: "One Piece",
      description:
        "Kid so focused on building a bird out of scrap metal, he doesn't realize his head got turned into a bird's nest.",
      gradientFrom: "from-orange-500",
      gradientTo: "to-red-600",

      categoryBadge: {
        text: "Manga",
        icon: "📖",
        variant: "manga" as const,
      },
      chapterInfo: {
        text: "Chapter 1012",
        onClick: () => console.log("Navigate to chapter"),
      },
      actionButton: {
        text: "READ",
        variant: "primary" as const,
        onClick: () => console.log("Start reading"),
      },
    },
    {
      id: "solo-leveling",
      title: "Solo Leveling",
      description:
        "Sung Jin-Woo's journey from the weakest hunter to the strongest shadow monarch continues.",
      gradientFrom: "from-purple-600",
      gradientTo: "to-blue-600",

      categoryBadge: {
        text: "Manhwa",
        icon: "📚",
        variant: "manhwa" as const,
      },
      chapterInfo: {
        text: "Chapter 154",
        onClick: () => console.log("Navigate to chapter"),
      },
      actionButton: {
        text: "READ",
        variant: "primary" as const,
        onClick: () => console.log("Start reading"),
      },
    },
    {
      id: "attack-on-titan",
      title: "Attack on Titan",
      description:
        "Humanity fights for survival against the mysterious Titans in this epic tale of courage and sacrifice.",
      gradientFrom: "from-green-600",
      gradientTo: "to-teal-600",

      categoryBadge: {
        text: "Manga",
        icon: "📖",
        variant: "manga" as const,
      },
      chapterInfo: {
        text: "Chapter 139",
        onClick: () => console.log("Navigate to chapter"),
      },
      actionButton: {
        text: "READ",
        variant: "primary" as const,
        onClick: () => console.log("Start reading"),
      },
    },
  ];

  // Categories data
  const categories: Category[] = [
    { id: "all-category", name: "All category" },
    { id: "shonen", name: "Shonen" },
    { id: "shojo", name: "Shojo" },
    { id: "seinen", name: "Seinen" },
    { id: "josei", name: "Josei" },
    { id: "kodomomuke", name: "Kodomomuke" },
    { id: "one-shot", name: "One Shot" },
    { id: "action", name: "Action" },
    { id: "adventure", name: "Adventure" },
    { id: "fantasy", name: "Fantasy" },
    { id: "dark-fantasy", name: "Dark Fantasy" },
    { id: "ecchi", name: "Ecchi" },
    { id: "romance", name: "Romance" },
    { id: "horror", name: "Horror" },
    { id: "parody", name: "Parody" },
    { id: "mystery", name: "Mystery" },
  ];

  // Sample manga data
  const popularManga: MangaData[] = [
    {
      id: "1",
      title: "One Piece",
      author: "Eiichiro Oda",
      chapter: "Chapter 1012",
      rating: 9.36,
      coverImage: "/api/placeholder/200/280",
      genres: ["Action", "Drama", "Fantasy", "Shounen", "Adventure"],
      status: "Ongoing",
    },
    {
      id: "2",
      title: "Solo Leveling",
      author: "Sung-Rak Jang",
      chapter: "Chapter 154",
      rating: 9.15,
      coverImage: "/api/placeholder/200/280",
      genres: ["Action", "Drama", "Fantasy", "Shounen", "Adventure"],
      status: "Completed",
    },
    {
      id: "3",
      title: "Berserk",
      author: "Kentaro Miura",
      chapter: "Chapter 368",
      rating: 9.32,
      coverImage: "/api/placeholder/200/280",
      genres: ["Dark Fantasy", "Drama", "Fantasy", "Adventure"],
      status: "Hiatus",
    },
    {
      id: "4",
      title: "The Beginning After The End",
      author: "TurtleMe",
      chapter: "Chapter 112",
      rating: 9.05,
      coverImage: "/api/placeholder/200/280",
      genres: ["Action", "Drama", "Fantasy", "Shounen", "Adventure"],
      status: "Ongoing",
    },
    {
      id: "5",
      title: "Versatile Mage",
      author: "Chaos",
      chapter: "Chapter 145",
      rating: 8.78,
      coverImage: "/api/placeholder/200/280",
      genres: ["Action", "Drama", "Fantasy", "Shounen", "Adventure"],
      status: "Ongoing",
    },
    {
      id: "6",
      title: "Attack on Titan",
      author: "Hajime Isayama",
      chapter: "Chapter 139",
      rating: 9.28,
      coverImage: "/api/placeholder/200/280",
      genres: ["Action", "Drama", "Fantasy", "Military"],
      status: "Completed",
    },
    {
      id: "7",
      title: "Demon Slayer",
      author: "Koyoharu Gotouge",
      chapter: "Chapter 205",
      rating: 8.95,
      coverImage: "/api/placeholder/200/280",
      genres: ["Action", "Supernatural", "Historical"],
      status: "Completed",
    },
    {
      id: "8",
      title: "Jujutsu Kaisen",
      author: "Gege Akutami",
      chapter: "Chapter 245",
      rating: 9.12,
      coverImage: "/api/placeholder/200/280",
      genres: ["Action", "Supernatural", "School"],
      status: "Ongoing",
    },
  ];

  const recentManga: MangaData[] = [
    ...popularManga.slice(0, 6).map((manga) => ({
      ...manga,
      id: `recent-${manga.id}`,
      chapter: `Chapter ${parseInt(manga.chapter.split(" ")[1]) + 1}`,
    })),
  ];

  const recommendedManga: MangaData[] = [
    ...popularManga.slice(2, 8).map((manga) => ({
      ...manga,
      id: `recommended-${manga.id}`,
    })),
  ];

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    console.log("Selected category:", categoryId);
  };

  const handleSlideChange = (slideIndex: number) => {
    console.log("Current slide:", slideIndex);
  };

  const handleMangaClick = (manga: MangaData) => {
    console.log("Clicked manga:", manga.title);
    // Navigate to manga detail page
  };

  return (
    <div className="bg-gray-50">
      {/* Hero Section */}
      <CarouselHeroBanner
        slides={heroSlides}
        height="lg"
        autoPlay={true}
        autoPlayInterval={3000}
        showNavigation={true}
        onSlideChange={handleSlideChange}
      />

      {/* Hot Categories Section - Hidden on mobile, shown on desktop */}
      <div className="hidden lg:block px-4 lg:px-6">
        <HotCategories
          categories={categories}
          selectedCategory={selectedCategory}
          onCategorySelect={handleCategorySelect}
          size="md"
          layout="flex"
          className="w-full"
        />
      </div>

      {/* Popular this month */}
      <div className="bg-[#1F1F1F] px-4 lg:px-6 py-8 lg:py-12">
        <PopularMangaCarousel
          mangas={popularManga}
          onMangaClick={handleMangaClick}
        />
      </div>

      {/* Recent uploads */}
      <div className="bg-[#1F1F1F] px-4 lg:px-6 py-8 lg:py-12">
        <RecentMangaCarousel
          mangas={recentManga}
          onMangaClick={handleMangaClick}
        />
      </div>

      {/* Recommended for you */}
      <div className="bg-[#1F1F1F] px-4 lg:px-6 py-8 lg:py-12">
        <RecommendedMangaCarousel
          mangas={recommendedManga}
          onMangaClick={handleMangaClick}
        />
      </div>
    </div>
  );
};
