import React, { useState } from "react";
import { Card } from "../components/ui/card";
import { Star } from "lucide-react";
import { CarouselHeroBanner } from "../components/common/CarouselHeroBanner";
import { HotCategories } from "../components/common/Categories";
import type { Category } from "../components/common/Categories";

export const HomePage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] =
    useState<string>("all-category");

  // Hero slides data
  const heroSlides = [
    {
      id: "one-piece",
      title: "One Piece",
      description:
        "Kid so focused on building a bird out of scrap metal, he doesn't realize his head got turned into a bird's nest.",
      gradientFrom: "from-orange-500",
      gradientTo: "to-red-600",

      categoryBadge: {
        text: "Manga",
        icon: "📖",
        variant: "manga" as const,
      },
      chapterInfo: {
        text: "Chapter 1012",
        onClick: () => console.log("Navigate to chapter"),
      },
      actionButton: {
        text: "READ",
        variant: "primary" as const,
        onClick: () => console.log("Start reading"),
      },
    },
    {
      id: "solo-leveling",
      title: "Solo Leveling",
      description:
        "Sung Jin-Woo's journey from the weakest hunter to the strongest shadow monarch continues.",
      gradientFrom: "from-purple-600",
      gradientTo: "to-blue-600",

      categoryBadge: {
        text: "Manhwa",
        icon: "📚",
        variant: "manhwa" as const,
      },
      chapterInfo: {
        text: "Chapter 154",
        onClick: () => console.log("Navigate to chapter"),
      },
      actionButton: {
        text: "READ",
        variant: "primary" as const,
        onClick: () => console.log("Start reading"),
      },
    },
    {
      id: "attack-on-titan",
      title: "Attack on Titan",
      description:
        "Humanity fights for survival against the mysterious Titans in this epic tale of courage and sacrifice.",
      gradientFrom: "from-green-600",
      gradientTo: "to-teal-600",

      categoryBadge: {
        text: "Manga",
        icon: "📖",
        variant: "manga" as const,
      },
      chapterInfo: {
        text: "Chapter 139",
        onClick: () => console.log("Navigate to chapter"),
      },
      actionButton: {
        text: "READ",
        variant: "primary" as const,
        onClick: () => console.log("Start reading"),
      },
    },
  ];

  // Categories data
  const categories: Category[] = [
    { id: "all-category", name: "All category" },
    { id: "shonen", name: "Shonen" },
    { id: "shojo", name: "Shojo" },
    { id: "seinen", name: "Seinen" },
    { id: "josei", name: "Josei" },
    { id: "kodomomuke", name: "Kodomomuke" },
    { id: "one-shot", name: "One Shot" },
    { id: "action", name: "Action" },
    { id: "adventure", name: "Adventure" },
    { id: "fantasy", name: "Fantasy" },
    { id: "dark-fantasy", name: "Dark Fantasy" },
    { id: "ecchi", name: "Ecchi" },
    { id: "romance", name: "Romance" },
    { id: "horror", name: "Horror" },
    { id: "parody", name: "Parody" },
    { id: "mystery", name: "Mystery" },
  ];

  const popularManga = [
    {
      id: 1,
      title: "One Piece",
      subtitle: "Eiichiro Oda",
      chapter: "Chapter 1012",
      rating: 9.36,
      image: "/api/placeholder/200/280",
      genres: ["Action", "Drama", "Fantasy", "Shounen", "Adventure"],
    },
    {
      id: 2,
      title: "Solo Leveling",
      subtitle: "Sung-Rak Jang",
      chapter: "154 Chapter",
      rating: 9.15,
      image: "/api/placeholder/200/280",
      genres: ["Action", "Drama", "Fantasy", "Shounen", "Adventure"],
    },
    {
      id: 3,
      title: "Berserk",
      subtitle: "Kentaro Miura",
      chapter: "368 Chapter",
      rating: 9.32,
      image: "/api/placeholder/200/280",
      genres: ["Dark Fantasy", "Drama", "Fantasy", "Adventure"],
    },
    {
      id: 4,
      title: "The beginning after the end",
      subtitle: "TurtleMe",
      chapter: "112 Chapter",
      rating: 9.05,
      image: "/api/placeholder/200/280",
      genres: ["Action", "Drama", "Fantasy", "Shounen", "Adventure"],
    },
    {
      id: 5,
      title: "Versatile Mage",
      subtitle: "Chaos",
      chapter: "145 Chapter",
      rating: 8.78,
      image: "/api/placeholder/200/280",
      genres: ["Action", "Drama", "Fantasy", "Shounen", "Adventure"],
    },
  ];

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    console.log("Selected category:", categoryId);
  };

  const handleSlideChange = (slideIndex: number) => {
    console.log("Current slide:", slideIndex);
  };

  return (
    <div className="bg-gray-50">
      {/* Hero Section */}
      <CarouselHeroBanner
        slides={heroSlides}
        height="lg"
        autoPlay={true}
        autoPlayInterval={3000}
        showNavigation={true}
        showPagination={false}
        onSlideChange={handleSlideChange}
        fixedBadge={{
          text: "Out now",
          icon: "🎉",
          variant: "default",
        }}
      />

      {/* Hot Categories Section - Hidden on mobile, shown on desktop */}
      <div className="hidden lg:block px-4 lg:px-6">
        <div className="mb-6">
          <HotCategories
            categories={categories}
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
            size="md"
            layout="flex"
            className="w-full"
          />
        </div>
      </div>

      {/* Popular this month */}
      <div className="px-4 lg:px-6">
        <div>
          <h2 className="text-xl lg:text-2xl font-bold mb-4 lg:mb-6">
            Popular <span className="text-[#F4B333]">this month</span>
          </h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 lg:gap-6">
            {popularManga.map((manga) => (
              <Card
                key={manga.id}
                className="overflow-hidden hover:shadow-lg transition-shadow"
              >
                <div className="relative">
                  <img
                    src={manga.image}
                    alt={manga.title}
                    className="w-full h-48 sm:h-56 lg:h-64 object-cover"
                  />
                  <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                    {manga.chapter}
                  </div>
                </div>
                <div className="p-3">
                  <h3 className="font-bold text-sm mb-1 truncate">
                    {manga.title}
                  </h3>
                  <p className="text-xs text-gray-600 mb-2">{manga.subtitle}</p>
                  <div className="flex flex-wrap gap-1 mb-2">
                    {manga.genres.slice(0, 3).map((genre) => (
                      <span
                        key={genre}
                        className="text-xs bg-gray-100 px-1 py-0.5 rounded"
                      >
                        {genre}
                      </span>
                    ))}
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    <span className="text-xs font-medium">{manga.rating}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
