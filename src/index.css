@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme - BlogTruyen Brand Colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Brand primary color: #E40066 (Pink/Magenta) */
    --primary: 340 100% 45%;
    --primary-foreground: 0 0% 100%;

    /* Brand secondary color: #03CEA4 (Teal/Cyan) */
    --secondary: 168 97% 41%;
    --secondary-foreground: 0 0% 100%;

    /* Brand accent color: #9747FF (Purple) */
    --accent: 260 100% 64%;
    --accent-foreground: 0 0% 100%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --destructive: 340 100% 45%;
    --destructive-foreground: 0 0% 100%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 340 100% 45%;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 217.2 32.6% 17.5%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Brand colors remain vibrant in dark mode */
    --primary: 340 100% 55%;
    --primary-foreground: 0 0% 100%;
    --secondary: 168 97% 51%;
    --secondary-foreground: 0 0% 100%;
    --accent: 260 100% 74%;
    --accent-foreground: 0 0% 100%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --destructive: 340 100% 55%;
    --destructive-foreground: 0 0% 100%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 340 100% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* Mobile Categories Animation */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.3s ease-out forwards;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Hide scrollbar for carousel */
  .scrollbar-hide {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }

  /* Manga carousel responsive display */
  .manga-carousel-container {
    /* Mobile: Show 2 cards */
    max-width: calc(2 * 200px + 1 * 16px); /* 2 cards + 1 gap */
  }

  @media (min-width: 640px) {
    .manga-carousel-container {
      /* Tablet: Show 3 cards */
      max-width: calc(3 * 200px + 2 * 16px); /* 3 cards + 2 gaps */
    }
  }

  @media (min-width: 1024px) {
    .manga-carousel-container {
      /* Desktop: Show 5 cards */
      max-width: calc(5 * 200px + 4 * 16px); /* 5 cards + 4 gaps */
    }
  }
}
