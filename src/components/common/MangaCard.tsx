import React, { useState } from "react";
import { Card } from "../ui/card";
import { <PERSON>, <PERSON>, Eye } from "lucide-react";
import { cn } from "../../utils/cn";

export interface MangaData {
  id: string;
  title: string;
  author: string;
  coverImage: string;
  chapter: string;
  rating: number;
  genres: string[];
  status: "Ongoing" | "Completed" | "Hiatus" | "Continuous";
  views?: number;
  updatedAt?: string;
  description?: string;
}

interface MangaCardProps {
  manga: MangaData;
  onClick?: (manga: MangaData) => void;
  size?: "sm" | "md" | "lg";
  showGenres?: boolean;
  showViews?: boolean;
  className?: string;
}

const sizeClasses = {
  sm: {
    card: "h-64",
    image: "h-32",
    title: "text-xs",
    author: "text-xs",
    chapter: "text-xs",
  },
  md: {
    card: "h-72 sm:h-80",
    image: "h-40 sm:h-48",
    title: "text-sm",
    author: "text-xs",
    chapter: "text-xs",
  },
  lg: {
    card: "h-80 sm:h-96",
    image: "h-48 sm:h-64",
    title: "text-sm sm:text-base",
    author: "text-xs sm:text-sm",
    chapter: "text-xs sm:text-sm",
  },
};

const statusColors = {
  Ongoing: "bg-green-500",
  Completed: "bg-blue-500",
  Hiatus: "bg-yellow-500",
  Continuous: "bg-purple-500",
};

export const MangaCard: React.FC<MangaCardProps> = ({
  manga,
  onClick,
  size = "md",
  showGenres = true,
  showViews = false,
  className = "",
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  const handleClick = () => {
    onClick?.(manga);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  const sizeConfig = sizeClasses[size];

  return (
    <Card
      className={cn(
        "overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 group bg-white",
        sizeConfig.card,
        className
      )}
      onClick={handleClick}
    >
      {/* Cover Image */}
      <div className={cn("relative overflow-hidden", sizeConfig.image)}>
        {imageLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
          </div>
        )}
        
        {!imageError ? (
          <img
            src={manga.coverImage}
            alt={manga.title}
            className={cn(
              "w-full h-full object-cover transition-transform duration-300 group-hover:scale-110",
              imageLoading ? "opacity-0" : "opacity-100"
            )}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
            <div className="text-gray-500 text-center">
              <div className="text-2xl mb-2">📚</div>
              <div className="text-xs">No Image</div>
            </div>
          </div>
        )}

        {/* Status Badge */}
        <div className="absolute top-2 right-2">
          <span
            className={cn(
              "px-2 py-1 text-xs font-medium text-white rounded-full",
              statusColors[manga.status]
            )}
          >
            {manga.status}
          </span>
        </div>

        {/* Chapter Badge */}
        <div className="absolute bottom-2 left-2">
          <span className="bg-black/70 text-white px-2 py-1 rounded text-xs font-medium">
            {manga.chapter}
          </span>
        </div>
      </div>

      {/* Card Content */}
      <div className="p-3 flex-1 flex flex-col">
        {/* Title */}
        <h3
          className={cn(
            "font-bold mb-1 line-clamp-2 group-hover:text-[#E40066] transition-colors",
            sizeConfig.title
          )}
          title={manga.title}
        >
          {manga.title}
        </h3>

        {/* Author */}
        <p
          className={cn(
            "text-gray-600 mb-2 truncate",
            sizeConfig.author
          )}
          title={manga.author}
        >
          {manga.author}
        </p>

        {/* Genres */}
        {showGenres && manga.genres.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-2">
            {manga.genres.slice(0, 3).map((genre) => (
              <span
                key={genre}
                className="text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full"
              >
                {genre}
              </span>
            ))}
            {manga.genres.length > 3 && (
              <span className="text-xs text-gray-500">
                +{manga.genres.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Bottom Info */}
        <div className="mt-auto flex items-center justify-between">
          {/* Rating */}
          <div className="flex items-center gap-1">
            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
            <span className="text-xs font-medium">{manga.rating}</span>
          </div>

          {/* Views */}
          {showViews && manga.views && (
            <div className="flex items-center gap-1 text-gray-500">
              <Eye className="w-3 h-3" />
              <span className="text-xs">
                {manga.views > 1000
                  ? `${(manga.views / 1000).toFixed(1)}k`
                  : manga.views}
              </span>
            </div>
          )}

          {/* Updated Time */}
          {manga.updatedAt && (
            <div className="flex items-center gap-1 text-gray-500">
              <Clock className="w-3 h-3" />
              <span className="text-xs">{manga.updatedAt}</span>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};
