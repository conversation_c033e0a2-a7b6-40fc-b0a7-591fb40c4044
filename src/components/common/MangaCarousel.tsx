import React, { useRef, useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { MangaCard, type MangaData } from "./MangaCard";
import { cn } from "../../utils/cn";

interface MangaCarouselProps {
  mangas: MangaData[];
  title: string;
  titleHighlight?: string;
  onMangaClick?: (manga: MangaData) => void;
  className?: string;
  titleClassName?: string;
  loading?: boolean;
}

const LoadingCard: React.FC = () => (
  <div
    className="flex-shrink-0 h-[320px]"
    style={{ width: "calc(20% - 12.8px)" }}
  >
    <div className="bg-white rounded-lg overflow-hidden animate-pulse border border-gray-200 h-full w-full">
      {/* Image skeleton */}
      <div className="bg-gray-200 h-[180px]"></div>

      {/* Title section skeleton */}
      <div className="bg-gray-300 p-3">
        <div className="h-4 bg-gray-400 rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-gray-400 rounded w-1/2"></div>
      </div>

      {/* Content skeleton */}
      <div className="p-3 space-y-2">
        <div className="flex justify-between items-center">
          <div className="h-3 bg-gray-200 rounded w-16"></div>
          <div className="h-3 bg-gray-200 rounded w-12"></div>
        </div>
        <div className="h-3 bg-gray-200 rounded w-20"></div>
      </div>
    </div>
  </div>
);

export const MangaCarousel: React.FC<MangaCarouselProps> = ({
  mangas,
  title,
  titleHighlight,
  onMangaClick,
  className = "",
  titleClassName = "",
  loading = false,
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
  }, [mangas]);

  const scroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const containerWidth = scrollContainerRef.current.clientWidth;
      const scrollAmount = containerWidth; // Scroll by full container width
      const newScrollLeft =
        direction === "left"
          ? scrollContainerRef.current.scrollLeft - scrollAmount
          : scrollContainerRef.current.scrollLeft + scrollAmount;

      scrollContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: "smooth",
      });
    }
  };
  const renderTitle = () => {
    if (!title && !titleHighlight) return null;

    return (
      <div className="flex items-center justify-between mb-6">
        <h2
          className={cn(
            "text-xl lg:text-2xl font-bold text-white",
            titleClassName
          )}
        >
          {title && <span>{title}</span>}
          {titleHighlight && (
            <span className="text-[#E40066] ml-1">{titleHighlight}</span>
          )}
        </h2>

        {/* Navigation buttons */}
        <div className="flex gap-2">
          <button
            onClick={() => scroll("left")}
            disabled={!canScrollLeft}
            className={cn(
              "p-2 rounded-full border transition-all duration-200",
              canScrollLeft
                ? "bg-gray-800 border-gray-600 hover:bg-gray-700 text-white"
                : "bg-gray-700 border-gray-600 text-gray-500 cursor-not-allowed"
            )}
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          <button
            onClick={() => scroll("right")}
            disabled={!canScrollRight}
            className={cn(
              "p-2 rounded-full border transition-all duration-200",
              canScrollRight
                ? "bg-gray-800 border-gray-600 hover:bg-gray-700 text-white"
                : "bg-gray-700 border-gray-600 text-gray-500 cursor-not-allowed"
            )}
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className="overflow-hidden">
          <div className="flex gap-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <LoadingCard key={index} />
            ))}
          </div>
        </div>
      );
    }

    if (!mangas.length) {
      return (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📚</div>
          <h3 className="text-lg font-medium text-white mb-2">
            No manga found
          </h3>
          <p className="text-gray-400">Try adjusting your search or filters</p>
        </div>
      );
    }

    return (
      <div className="overflow-hidden">
        <div
          ref={scrollContainerRef}
          className="flex gap-4 overflow-x-auto scrollbar-hide pb-2"
          onScroll={checkScrollButtons}
          style={{
            scrollbarWidth: "none",
            msOverflowStyle: "none",
          }}
        >
          {mangas.map((manga) => (
            <div
              key={manga.id}
              className="flex-shrink-0"
              style={{ width: "calc(20% - 12.8px)" }}
            >
              <MangaCard manga={manga} onClick={onMangaClick} />
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {renderTitle()}
      {renderContent()}
    </div>
  );
};

// Preset variants for the 3 sections
export const PopularMangaCarousel: React.FC<
  Omit<MangaCarouselProps, "title" | "titleHighlight">
> = (props) => (
  <MangaCarousel title="Popular" titleHighlight="this month" {...props} />
);

export const RecentMangaCarousel: React.FC<
  Omit<MangaCarouselProps, "title" | "titleHighlight">
> = (props) => (
  <MangaCarousel title="Recent" titleHighlight="uploads" {...props} />
);

export const RecommendedMangaCarousel: React.FC<
  Omit<MangaCarouselProps, "title" | "titleHighlight">
> = (props) => (
  <MangaCarousel title="Recommended" titleHighlight="for you" {...props} />
);
