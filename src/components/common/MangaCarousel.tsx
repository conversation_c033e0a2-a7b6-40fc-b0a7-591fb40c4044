import React from "react";
import { MangaCard, type MangaData } from "./MangaCard";
import { cn } from "../../utils/cn";

interface MangaCarouselProps {
  mangas: MangaData[];
  title: string;
  titleHighlight?: string;
  onMangaClick?: (manga: MangaData) => void;
  className?: string;
  titleClassName?: string;
  loading?: boolean;
  loadingCount?: number;
}

const LoadingCard: React.FC = () => (
  <div className="w-full h-[320px]">
    <div className="bg-white rounded-lg overflow-hidden animate-pulse border border-gray-200 h-full">
      {/* Image skeleton */}
      <div className="bg-gray-200 h-[180px]"></div>

      {/* Title section skeleton */}
      <div className="bg-gray-300 p-3">
        <div className="h-4 bg-gray-400 rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-gray-400 rounded w-1/2"></div>
      </div>

      {/* Content skeleton */}
      <div className="p-3 space-y-2">
        <div className="flex justify-between items-center">
          <div className="h-3 bg-gray-200 rounded w-16"></div>
          <div className="h-3 bg-gray-200 rounded w-12"></div>
        </div>
        <div className="h-3 bg-gray-200 rounded w-20"></div>
      </div>
    </div>
  </div>
);

export const MangaCarousel: React.FC<MangaCarouselProps> = ({
  mangas,
  title,
  titleHighlight,
  onMangaClick,
  className = "",
  titleClassName = "",
  loading = false,
  loadingCount = 8,
}) => {
  const renderTitle = () => {
    if (!title && !titleHighlight) return null;

    return (
      <div className="flex items-center justify-between mb-6">
        <h2
          className={cn(
            "text-xl lg:text-2xl font-bold text-white",
            titleClassName
          )}
        >
          {title && <span>{title}</span>}
          {titleHighlight && (
            <span className="text-[#E40066] ml-1">{titleHighlight}</span>
          )}
        </h2>
      </div>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
          {Array.from({ length: loadingCount }).map((_, index) => (
            <LoadingCard key={index} />
          ))}
        </div>
      );
    }

    if (!mangas.length) {
      return (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📚</div>
          <h3 className="text-lg font-medium text-white mb-2">
            No manga found
          </h3>
          <p className="text-gray-400">Try adjusting your search or filters</p>
        </div>
      );
    }

    return (
      <div className="w-full">
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
          {mangas.slice(0, 10).map((manga) => (
            <MangaCard key={manga.id} manga={manga} onClick={onMangaClick} />
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {renderTitle()}
      {renderContent()}
    </div>
  );
};

// Preset variants for the 3 sections
export const PopularMangaCarousel: React.FC<
  Omit<MangaCarouselProps, "title" | "titleHighlight">
> = (props) => (
  <MangaCarousel title="Popular" titleHighlight="this month" {...props} />
);

export const RecentMangaCarousel: React.FC<
  Omit<MangaCarouselProps, "title" | "titleHighlight">
> = (props) => (
  <MangaCarousel title="Recent" titleHighlight="uploads" {...props} />
);

export const RecommendedMangaCarousel: React.FC<
  Omit<MangaCarouselProps, "title" | "titleHighlight">
> = (props) => (
  <MangaCarousel title="Recommended" titleHighlight="for you" {...props} />
);
